@echo off
REM Kritrima-AI WSL Build and Install - Double-click to run
REM This batch file launches the PowerShell script with proper execution policy

echo.
echo ========================================
echo   Kritrima-AI WSL Build and Install
echo ========================================
echo.
echo This will build and install Kritrima-AI in WSL.
echo It will automatically fix common build errors.
echo.

REM Check if PowerShell is available
where powershell >nul 2>nul
if %errorlevel% neq 0 (
    echo ERROR: PowerShell is not available.
    echo Please install PowerShell or run from PowerShell directly.
    pause
    exit /b 1
)

REM Get the directory where this batch file is located
set "SCRIPT_DIR=%~dp0"

REM Run the PowerShell script with bypass execution policy
echo Starting build process...
echo.
powershell -ExecutionPolicy Bypass -File "%SCRIPT_DIR%build-and-install-wsl.ps1"

REM Check if the PowerShell script succeeded
if %errorlevel% equ 0 (
    echo.
    echo ========================================
    echo   Build completed successfully!
    echo ========================================
    echo.
    echo You can now use 'kritrima-ai' in WSL.
    echo.
    echo To test: 
    echo   1. Open WSL: wsl
    echo   2. Run: kritrima-ai --help
    echo.
) else (
    echo.
    echo ========================================
    echo   Build failed!
    echo ========================================
    echo.
    echo Please check the error messages above.
    echo You may need to run the script as administrator.
    echo.
)

echo Press any key to exit...
pause >nul

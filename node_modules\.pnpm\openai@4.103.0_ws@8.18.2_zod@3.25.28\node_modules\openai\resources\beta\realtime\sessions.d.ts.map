{"version": 3, "file": "sessions.d.ts", "sourceRoot": "", "sources": ["../../../src/resources/beta/realtime/sessions.ts"], "names": [], "mappings": "AAEA,OAAO,EAAE,WAAW,EAAE,MAAM,mBAAmB,CAAC;AAChD,OAAO,KAAK,IAAI,MAAM,eAAe,CAAC;AAEtC,qBAAa,QAAS,SAAQ,WAAW;IACvC;;;;;;;;;;;;;;OAcG;IACH,MAAM,CAAC,IAAI,EAAE,mBAAmB,EAAE,OAAO,CAAC,EAAE,IAAI,CAAC,cAAc,GAAG,IAAI,CAAC,UAAU,CAAC,qBAAqB,CAAC;CAOzG;AAED;;GAEG;AACH,MAAM,WAAW,OAAO;IACtB;;OAEG;IACH,EAAE,CAAC,EAAE,MAAM,CAAC;IAEZ;;;;OAIG;IACH,kBAAkB,CAAC,EAAE,OAAO,GAAG,WAAW,GAAG,WAAW,CAAC;IAEzD;;;;;;OAMG;IACH,2BAA2B,CAAC,EAAE,OAAO,CAAC,wBAAwB,CAAC;IAE/D;;;;;;;;;OASG;IACH,yBAAyB,CAAC,EAAE,OAAO,CAAC,uBAAuB,CAAC;IAE5D;;;;;;;;;;;;OAYG;IACH,YAAY,CAAC,EAAE,MAAM,CAAC;IAEtB;;;;OAIG;IACH,0BAA0B,CAAC,EAAE,MAAM,GAAG,KAAK,CAAC;IAE5C;;;OAGG;IACH,UAAU,CAAC,EAAE,KAAK,CAAC,MAAM,GAAG,OAAO,CAAC,CAAC;IAErC;;OAEG;IACH,KAAK,CAAC,EACF,yBAAyB,GACzB,oCAAoC,GACpC,oCAAoC,GACpC,8BAA8B,GAC9B,yCAAyC,CAAC;IAE9C;;;OAGG;IACH,mBAAmB,CAAC,EAAE,OAAO,GAAG,WAAW,GAAG,WAAW,CAAC;IAE1D;;;OAGG;IACH,WAAW,CAAC,EAAE,MAAM,CAAC;IAErB;;;OAGG;IACH,WAAW,CAAC,EAAE,MAAM,CAAC;IAErB;;OAEG;IACH,KAAK,CAAC,EAAE,KAAK,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;IAE5B;;;;;;;;;;;OAWG;IACH,cAAc,CAAC,EAAE,OAAO,CAAC,aAAa,CAAC;IAEvC;;;;OAIG;IACH,KAAK,CAAC,EACF,CAAC,MAAM,GAAG,EAAE,CAAC,GACb,OAAO,GACP,KAAK,GACL,QAAQ,GACR,OAAO,GACP,MAAM,GACN,OAAO,GACP,MAAM,GACN,MAAM,GACN,MAAM,GACN,SAAS,GACT,OAAO,CAAC;CACb;AAED,yBAAiB,OAAO,CAAC;IACvB;;;;;;OAMG;IACH,UAAiB,wBAAwB;QACvC;;;;WAIG;QACH,IAAI,CAAC,EAAE,YAAY,GAAG,WAAW,CAAC;KACnC;IAED;;;;;;;;;OASG;IACH,UAAiB,uBAAuB;QACtC;;;;WAIG;QACH,QAAQ,CAAC,EAAE,MAAM,CAAC;QAElB;;;WAGG;QACH,KAAK,CAAC,EAAE,MAAM,CAAC;QAEf;;;;;;WAMG;QACH,MAAM,CAAC,EAAE,MAAM,CAAC;KACjB;IAED,UAAiB,IAAI;QACnB;;;WAGG;QACH,WAAW,CAAC,EAAE,MAAM,CAAC;QAErB;;WAEG;QACH,IAAI,CAAC,EAAE,MAAM,CAAC;QAEd;;WAEG;QACH,UAAU,CAAC,EAAE,OAAO,CAAC;QAErB;;WAEG;QACH,IAAI,CAAC,EAAE,UAAU,CAAC;KACnB;IAED;;;;;;;;;;;OAWG;IACH,UAAiB,aAAa;QAC5B;;;WAGG;QACH,eAAe,CAAC,EAAE,OAAO,CAAC;QAE1B;;;;WAIG;QACH,SAAS,CAAC,EAAE,KAAK,GAAG,QAAQ,GAAG,MAAM,GAAG,MAAM,CAAC;QAE/C;;;;WAIG;QACH,kBAAkB,CAAC,EAAE,OAAO,CAAC;QAE7B;;;WAGG;QACH,iBAAiB,CAAC,EAAE,MAAM,CAAC;QAE3B;;;;WAIG;QACH,mBAAmB,CAAC,EAAE,MAAM,CAAC;QAE7B;;;;WAIG;QACH,SAAS,CAAC,EAAE,MAAM,CAAC;QAEnB;;WAEG;QACH,IAAI,CAAC,EAAE,YAAY,GAAG,cAAc,CAAC;KACtC;CACF;AAED;;;GAGG;AACH,MAAM,WAAW,qBAAqB;IACpC;;OAEG;IACH,aAAa,EAAE,qBAAqB,CAAC,YAAY,CAAC;IAElD;;OAEG;IACH,kBAAkB,CAAC,EAAE,MAAM,CAAC;IAE5B;;;;;;OAMG;IACH,yBAAyB,CAAC,EAAE,qBAAqB,CAAC,uBAAuB,CAAC;IAE1E;;;;;;;;;;;;OAYG;IACH,YAAY,CAAC,EAAE,MAAM,CAAC;IAEtB;;;;OAIG;IACH,0BAA0B,CAAC,EAAE,MAAM,GAAG,KAAK,CAAC;IAE5C;;;OAGG;IACH,UAAU,CAAC,EAAE,KAAK,CAAC,MAAM,GAAG,OAAO,CAAC,CAAC;IAErC;;OAEG;IACH,mBAAmB,CAAC,EAAE,MAAM,CAAC;IAE7B;;OAEG;IACH,WAAW,CAAC,EAAE,MAAM,CAAC;IAErB;;;OAGG;IACH,WAAW,CAAC,EAAE,MAAM,CAAC;IAErB;;OAEG;IACH,KAAK,CAAC,EAAE,KAAK,CAAC,qBAAqB,CAAC,IAAI,CAAC,CAAC;IAE1C;;;;OAIG;IACH,cAAc,CAAC,EAAE,qBAAqB,CAAC,aAAa,CAAC;IAErD;;;;OAIG;IACH,KAAK,CAAC,EACF,CAAC,MAAM,GAAG,EAAE,CAAC,GACb,OAAO,GACP,KAAK,GACL,QAAQ,GACR,OAAO,GACP,MAAM,GACN,OAAO,GACP,MAAM,GACN,MAAM,GACN,MAAM,GACN,SAAS,GACT,OAAO,CAAC;CACb;AAED,yBAAiB,qBAAqB,CAAC;IACrC;;OAEG;IACH,UAAiB,YAAY;QAC3B;;;WAGG;QACH,UAAU,EAAE,MAAM,CAAC;QAEnB;;;;WAIG;QACH,KAAK,EAAE,MAAM,CAAC;KACf;IAED;;;;;;OAMG;IACH,UAAiB,uBAAuB;QACtC;;;WAGG;QACH,KAAK,CAAC,EAAE,MAAM,CAAC;KAChB;IAED,UAAiB,IAAI;QACnB;;;WAGG;QACH,WAAW,CAAC,EAAE,MAAM,CAAC;QAErB;;WAEG;QACH,IAAI,CAAC,EAAE,MAAM,CAAC;QAEd;;WAEG;QACH,UAAU,CAAC,EAAE,OAAO,CAAC;QAErB;;WAEG;QACH,IAAI,CAAC,EAAE,UAAU,CAAC;KACnB;IAED;;;;OAIG;IACH,UAAiB,aAAa;QAC5B;;;WAGG;QACH,iBAAiB,CAAC,EAAE,MAAM,CAAC;QAE3B;;;;WAIG;QACH,mBAAmB,CAAC,EAAE,MAAM,CAAC;QAE7B;;;;WAIG;QACH,SAAS,CAAC,EAAE,MAAM,CAAC;QAEnB;;WAEG;QACH,IAAI,CAAC,EAAE,MAAM,CAAC;KACf;CACF;AAED,MAAM,WAAW,mBAAmB;IAClC;;;;OAIG;IACH,kBAAkB,CAAC,EAAE,OAAO,GAAG,WAAW,GAAG,WAAW,CAAC;IAEzD;;;;;;OAMG;IACH,2BAA2B,CAAC,EAAE,mBAAmB,CAAC,wBAAwB,CAAC;IAE3E;;;;;;;;;OASG;IACH,yBAAyB,CAAC,EAAE,mBAAmB,CAAC,uBAAuB,CAAC;IAExE;;;;;;;;;;;;OAYG;IACH,YAAY,CAAC,EAAE,MAAM,CAAC;IAEtB;;;;OAIG;IACH,0BAA0B,CAAC,EAAE,MAAM,GAAG,KAAK,CAAC;IAE5C;;;OAGG;IACH,UAAU,CAAC,EAAE,KAAK,CAAC,MAAM,GAAG,OAAO,CAAC,CAAC;IAErC;;OAEG;IACH,KAAK,CAAC,EACF,yBAAyB,GACzB,oCAAoC,GACpC,oCAAoC,GACpC,8BAA8B,GAC9B,yCAAyC,CAAC;IAE9C;;;OAGG;IACH,mBAAmB,CAAC,EAAE,OAAO,GAAG,WAAW,GAAG,WAAW,CAAC;IAE1D;;;OAGG;IACH,WAAW,CAAC,EAAE,MAAM,CAAC;IAErB;;;OAGG;IACH,WAAW,CAAC,EAAE,MAAM,CAAC;IAErB;;OAEG;IACH,KAAK,CAAC,EAAE,KAAK,CAAC,mBAAmB,CAAC,IAAI,CAAC,CAAC;IAExC;;;;;;;;;;;OAWG;IACH,cAAc,CAAC,EAAE,mBAAmB,CAAC,aAAa,CAAC;IAEnD;;;;;OAKG;IACH,KAAK,CAAC,EACF,CAAC,MAAM,GAAG,EAAE,CAAC,GACb,OAAO,GACP,KAAK,GACL,QAAQ,GACR,OAAO,GACP,MAAM,GACN,OAAO,GACP,MAAM,GACN,MAAM,GACN,MAAM,GACN,SAAS,GACT,OAAO,CAAC;CACb;AAED,yBAAiB,mBAAmB,CAAC;IACnC;;;;;;OAMG;IACH,UAAiB,wBAAwB;QACvC;;;;WAIG;QACH,IAAI,CAAC,EAAE,YAAY,GAAG,WAAW,CAAC;KACnC;IAED;;;;;;;;;OASG;IACH,UAAiB,uBAAuB;QACtC;;;;WAIG;QACH,QAAQ,CAAC,EAAE,MAAM,CAAC;QAElB;;;WAGG;QACH,KAAK,CAAC,EAAE,MAAM,CAAC;QAEf;;;;;;WAMG;QACH,MAAM,CAAC,EAAE,MAAM,CAAC;KACjB;IAED,UAAiB,IAAI;QACnB;;;WAGG;QACH,WAAW,CAAC,EAAE,MAAM,CAAC;QAErB;;WAEG;QACH,IAAI,CAAC,EAAE,MAAM,CAAC;QAEd;;WAEG;QACH,UAAU,CAAC,EAAE,OAAO,CAAC;QAErB;;WAEG;QACH,IAAI,CAAC,EAAE,UAAU,CAAC;KACnB;IAED;;;;;;;;;;;OAWG;IACH,UAAiB,aAAa;QAC5B;;;WAGG;QACH,eAAe,CAAC,EAAE,OAAO,CAAC;QAE1B;;;;WAIG;QACH,SAAS,CAAC,EAAE,KAAK,GAAG,QAAQ,GAAG,MAAM,GAAG,MAAM,CAAC;QAE/C;;;;WAIG;QACH,kBAAkB,CAAC,EAAE,OAAO,CAAC;QAE7B;;;WAGG;QACH,iBAAiB,CAAC,EAAE,MAAM,CAAC;QAE3B;;;;WAIG;QACH,mBAAmB,CAAC,EAAE,MAAM,CAAC;QAE7B;;;;WAIG;QACH,SAAS,CAAC,EAAE,MAAM,CAAC;QAEnB;;WAEG;QACH,IAAI,CAAC,EAAE,YAAY,GAAG,cAAc,CAAC;KACtC;CACF;AAED,MAAM,CAAC,OAAO,WAAW,QAAQ,CAAC;IAChC,OAAO,EACL,KAAK,OAAO,IAAI,OAAO,EACvB,KAAK,qBAAqB,IAAI,qBAAqB,EACnD,KAAK,mBAAmB,IAAI,mBAAmB,GAChD,CAAC;CACH"}
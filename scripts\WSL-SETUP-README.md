# Kritrima-AI WSL Setup Guide

This guide helps you set up and build Kritrima-AI in Windows Subsystem for Linux (WSL), automatically fixing common build errors.

## 🚀 Quick Start (Automated)

### Option 1: Double-click Installation
1. **Double-click** `build-and-install-wsl.bat` in Windows Explorer
2. The script will automatically:
   - Detect your project location
   - Fix platform-specific build issues
   - Install dependencies with npm (more reliable than pnpm in WSL)
   - Build the project successfully
   - Install Kritrima-AI globally
   - Test the installation

### Option 2: PowerShell
```powershell
# Run from the scripts directory
.\build-and-install-wsl.ps1

# Or with options
.\build-and-install-wsl.ps1 -Clean  # Clean build artifacts first
.\build-and-install-wsl.ps1 -ProjectPath "C:\Your\Project\Path"
```

## 🔧 What the Scripts Fix

The automated scripts resolve these common WSL build issues:

### 1. **esbuild Platform Mismatch**
- **Problem**: `@esbuild/win32-x64` package present but WSL needs `@esbuild/linux-x64`
- **Solution**: Clean node_modules and reinstall with npm in WSL environment

### 2. **PNPM Permission Errors**
- **Problem**: `EACCES: permission denied` when using pnpm in WSL with Windows filesystem
- **Solution**: Use npm instead of pnpm for WSL installations

### 3. **Missing dist/cli.js**
- **Problem**: Build fails, so output file doesn't exist
- **Solution**: Ensure clean build environment and proper dependency installation

### 4. **Cross-platform File System Issues**
- **Problem**: Windows/WSL filesystem permission conflicts
- **Solution**: Proper path handling and permission management

## 📋 Manual Setup (If Needed)

If you prefer manual setup or need to troubleshoot:

### Prerequisites
```bash
# In WSL, install Node.js 22+
curl -fsSL https://deb.nodesource.com/setup_22.x | sudo -E bash -
sudo apt-get install -y nodejs npm build-essential

# Set environment variables
export KRITRIMA_AI_UNSAFE_ALLOW_NO_SANDBOX=1
export NODE_OPTIONS='--max-old-space-size=4096'
```

### Manual Build Process
```bash
# Navigate to project in WSL
cd "/mnt/c/Users/<USER>/OneDrive/Documents/Kritrima AI"

# Clean previous builds (important!)
rm -rf node_modules dist .pnpm-store

# Install with npm (not pnpm)
npm install

# Build the project
npm run build

# Install globally
npm install -g .

# Test
kritrima-ai --version
kritrima-ai --help
```

## 🎯 Usage After Installation

Once installed, you can use Kritrima-AI from any WSL terminal:

```bash
# Get help
kritrima-ai --help

# Basic usage
kritrima-ai "explain this codebase"

# With specific options
kritrima-ai -q "fix build issues"
kritrima-ai --model gpt-4 "write a Python script"
```

## 🛠️ Troubleshooting

### Build Still Fails?
1. **Run as Administrator**: Right-click batch file → "Run as administrator"
2. **Clean Build**: Use `-Clean` flag to remove all artifacts
3. **Check WSL**: Ensure WSL is properly installed and updated
4. **Node Version**: Verify Node.js 22+ is installed in WSL

### Permission Issues?
```bash
# In WSL, check permissions
ls -la "/mnt/c/Users/<USER>/OneDrive/Documents/Kritrima AI"

# Fix if needed
sudo chown -R $USER:$USER "/mnt/c/Users/<USER>/OneDrive/Documents/Kritrima AI"
```

### esbuild Still Complaining?
```bash
# Force clean and reinstall
rm -rf node_modules package-lock.json
npm cache clean --force
npm install
```

## 📁 Script Files

- **`build-and-install-wsl.bat`**: Double-click installer for Windows
- **`build-and-install-wsl.ps1`**: PowerShell script with full automation
- **`setup-wsl.ps1`**: WSL environment setup (if needed)

## 🔍 What's Different in WSL?

WSL requires special handling because:

1. **File System**: Windows NTFS mounted in Linux environment
2. **Permissions**: Different permission models between Windows and Linux
3. **Package Managers**: pnpm has issues with cross-platform file operations
4. **Build Tools**: Native binaries need Linux versions, not Windows versions

The automated scripts handle all these differences transparently.

## ✅ Success Indicators

You'll know it worked when you see:
```
✅ Kritrima-AI installed successfully!
📋 Version: 0.0.0-dev
🎉 Installation complete! You can now use 'kritrima-ai' command.
```

## 🆘 Getting Help

If you encounter issues:

1. **Check the output**: The scripts provide detailed error messages
2. **Try clean build**: Use the `-Clean` flag
3. **Verify WSL**: Run `wsl --version` in PowerShell
4. **Check Node**: Run `node --version` in WSL (should be 22+)

## 🎉 Next Steps

After successful installation:

1. **Set up API key**: Follow the prompts when first running kritrima-ai
2. **Explore features**: Try `kritrima-ai --help` for all options
3. **Start coding**: Use kritrima-ai to help with your development tasks!

---

*This setup process has been tested on Windows 11 with WSL 2 and Ubuntu.*
